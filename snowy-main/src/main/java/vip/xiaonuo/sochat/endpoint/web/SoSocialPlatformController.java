package vip.xiaonuo.sochat.endpoint.web;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;
import vip.xiaonuo.core.context.login.LoginContextHolder;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import vip.xiaonuo.core.query.SqlDao;
import vip.xiaonuo.sochat.domain.model.PlatformDataPredicateManager;
import vip.xiaonuo.sochat.domain.model.ScPlatformScript;
import vip.xiaonuo.sochat.domain.model.ScPlatformScriptRepository;
import vip.xiaonuo.sochat.domain.model.SoChatException;
import vip.xiaonuo.sochat.io.vo.SocialPlatform;

import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ${@linkplain @/api/modular/system/soChatTenantService.js}
 */
@ApiIgnore
@Api(tags = "Web端-平台信息")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
@RestController
public class SoSocialPlatformController {

    @Resource
    private final SqlDao sqlDao;

    @Resource
    private final ScPlatformScriptRepository repository;

    @Resource
    private final PlatformDataPredicateManager manager;

    @GetMapping(value = "/soChat/platform/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseData listPlatform() {
        List<Map<String, Object>> result = sqlDao.findListAliasToMap("select id" +
                ", p.name" +
                ", p.logo" +
                ", p.count_enabled countEnabled" +
                ", p.short_name shortName" +
                ", p.contact_url_template contactUrlTemplate" +
                " from sc_platform p" +
                " where p.id > 1" +
                " order by p.sort, p.id");

        Map<String, Map<String, Object>> map = result.stream()
                .collect(Collectors.toMap(x -> (String) x.get("name"), x -> x, (x1, x2) -> x1, LinkedHashMap::new));

        return new SuccessResponseData(map);
    }

    @GetMapping(value = "/soChat/platform/sensitiveRefList", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseData listSensitiveRefPlatform() {
        List<Map<String, Object>> result = sqlDao.findListAliasToMap("select id" +
                ", p.name" +
                ", p.logo" +
                ", p.count_enabled countEnabled" +
                ", p.short_name shortName" +
                ", p.contact_url_template contactUrlTemplate" +
                " from sc_platform p" +
                " where p.id > 1" +
                " order by p.sort, p.id");

        Map<String, String> map = SocialPlatform.sensitiveRefList().
                stream()
                .collect(Collectors.toMap(x -> String.valueOf(x.id()), x -> x.getFullName(), (x1, x2) -> x1, LinkedHashMap::new));

        return new SuccessResponseData(map);
    }

    @PostMapping(value = "/soChat/cmd/getPlatformScript", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseData getPlatformScript(@RequestBody Map<String, Object> map) {
        if (LoginContextHolder.me().isSuperAdmin()) {
            final Object o = map.get("platform");
            if (o instanceof String) {
                return SuccessResponseData.success(repository.get((String) o));
            }
        }
        throw new SoChatException(SoChatException.ERROR_CODE.SYS_ADMIN_PERMISSION_NEED);
    }

    @PostMapping(value = "/soChat/cmd/updatePlatformScript", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseData updatePlatformScript(@RequestBody ScPlatformScript script) {
        if (LoginContextHolder.me().isSuperAdmin()) {
            manager.update(script.getPlatform(), script.getAccept(), script.getAdapter(), script.getIsContact(), script.getIsGroup());
            return SuccessResponseData.success();
        }
        throw new SoChatException(SoChatException.ERROR_CODE.SYS_ADMIN_PERMISSION_NEED);
    }
}
